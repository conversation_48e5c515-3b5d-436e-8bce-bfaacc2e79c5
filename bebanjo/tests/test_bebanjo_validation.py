#!/usr/bin/env python3
"""
Test script to validate the BebanjoPayloadSchema Pydantic model
with the provided Bebanjo payload JSON data.
"""

import json

from pydantic import ValidationError
from utils.schemas.payloads import BebanjoPayloadSchema

# Sample Bebanjo payload from the user's request
bebanjo_payload = {
    "eventId": 0,
    "transactionTime": None,
    "vod4WindowStart": None,
    "vod4WindowEnd": None,
    "vod8WindowStart": None,
    "vod8WindowEnd": None,
    "c3WindowStart": None,
    "c3WindowEnd": None,
    "c7WindowStart": None,
    "c7WindowEnd": None,
    "cleanStart": None,
    "cleanEnd": None,
    "changeType": "A",
    "network": "DC",
    "uniqueId": "mass0000000021708936",
    "trafficCode": "K100014340",
    "platformName": "VOD FVOD DC HD LF",
    "provider": "DISNEY_CHANNEL_HD",
    "vodTitle": "MLBUG2-02_HD_D_08-01_title",
    "vodShortTitle": "Ladybug_S02_E02_HD",
    "episodeVodTitle": None,
    "episodeVodShortTitle": "Prime Queen",
    "episodeName": "Prime Queen",
    "episodeID": "2",
    "summaryShort": "Nadia Chamack is akumatized into Prime Queen. Armed with her smartwatch, she forces Ladybug and Cat Noir to say they're in love on live TV!",
    "rating": "TV-Y7",
    "episodeRating": "",
    "movieRating": None,
    "displayRunTime": "00:30:00",
    "year": 2017,
    "cmcCategories": [
        "Disney Channel HD/Mrcls Ladybug"
    ],
    "closedCaptioning": "Y",
    "radarProductId": "182416",
    "genres": "Family",
    "actors": "",
    "licensingWindowStart": "2025-08-01T07:00:00Z",
    "licensingWindowEnd": "2025-09-01T06:59:00Z",
    "episodeOriginalAirDate": True,
    "scheduleMaterials": "[ {\n  \"tns:AssetName\" : \"DEPP2963\",\n  \"tns:PlaylistPosition\" : 1,\n  \"tns:SegmentNumber\" : null\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 2,\n  \"tns:SegmentNumber\" : 1\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 3,\n  \"tns:SegmentNumber\" : 2\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 4,\n  \"tns:SegmentNumber\" : 3\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 5,\n  \"tns:SegmentNumber\" : 4\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 6,\n  \"tns:SegmentNumber\" : 5\n} ]",
    "showTitle": "Miraculous: Tales of Ladybug & Cat Noir",
    "seasonNumber": 2,
    "showType": "TVShow",
    "episodeNumber": 2,
    "airDate": "2025-08-01T07:00:00Z",
    "createDate": "2025-08-01T07:00:00Z",
    "oneLineDescription": "It's lights, camera, action for Ladybug and Cat Noir!",
    "originalAirdate": "2017-10-19T07:00:00Z",
    "artworksUrl": "https://movida-staging.s3.amazonaws.com/images/attachments/2227862/original/1555008480-4409b1f68a35ad823d0e423226413d29b3b0144b.bmp",
    "artworksType": "poster",
    "artworksFileName": "DC_MIR_STB_320x240_14059192.bmp",
    "assetType": "VOD",
    "assetName": "MLBUG2-02_HD_D_08-01",
    "xmlFileName": "DIS_ADC0000000021708936V.xml",
    "videoFileName": "DIS_ADC0000000021708936V.mpg",
    "publishedDateTime": None,
    "s3FileLocation": "mds-bebanjo-schedules-dev/Kids/2025/5/VOD_FVOD_DC_HD_LF-mass0000000021708936-20250527T144923495.xml",
    "assetIdPackage": "pack0000000021708936",
    "assetNamePackage": "MLBUG2-02_HD_D_08-01",
    "assetIdTitle": "tass0000000021708936",
    "assetNameTitle": "MLBUG2-02_HD_D_08-01_title",
    "seriesId": "",
    "seriesName": "Miraculous Ladybug",
    "providerId": "disneychannel.com",
    "descriptionPackage": "MLBUG2-02_HD_D_08-01 asset package",
    "descriptionTitle": "MLBUG2-02_HD_D_08-01 title asset",
    "product": "DISCHD",
    "titleBrief": "Ladybug_S02_E02_HD",
    "billingID": "00000",
    "providerQaContact": "<EMAIL>",
    "assetIdMovie": "mass0000000021708936",
    "assetNameMovie": "MLBUG2-02_HD_D_08-01_movie",
    "descriptionMovie": "MLBUG2-02_HD_D_08-01 movie asset",
    "hdContent": "Y",
    "audioType": "Dolby 5.1",
    "languages": "en",
    "trickModesRestricted": "n/a",
    "assetIdPost": "post0000000021708936",
    "assetNamePost": "MLBUG2-02_HD_D_08-01_poster",
    "descriptionPost": "MLBUG2-02_HD_D_08-01 poster asset",
    "contentFileSizeImage": 230456,
    "contentCheckSumImage": "2ff6b4fce128eb85073587d3c8a8c970",
    "seasonPremiere": None,
    "seasonFinale": None,
    "titleSortName": None,
    "countryOfOrigin": None,
    "authority": None,
    "contentRatingDescriptors": "",
    "showCode": "MLBUG",
    "placingId": 263980712
}

def test_valid_payload():
    """Test that the complete payload validates successfully."""
    print("Testing valid payload...")
    try:
        validated_payload = BebanjoPayloadSchema.model_validate(bebanjo_payload)
        print("✅ Valid payload passed validation!")
        print(f"   - event_id: {validated_payload.event_id}")
        print(f"   - network: {validated_payload.network}")
        print(f"   - show_title: {validated_payload.show_title}")
        print(f"   - asset_name: {validated_payload.asset_name}")
        print(f"   - season_number: {validated_payload.season_number}")
        print(f"   - episode_number: {validated_payload.episode_number}")
        return True
    except ValidationError as e:
        print(f"❌ Valid payload failed validation: {e}")
        return False

def test_missing_required_fields():
    """Test that missing required fields cause validation errors."""
    print("\nTesting missing required fields...")
    
    # Test missing eventId (maps to event_id)
    incomplete_payload = bebanjo_payload.copy()
    del incomplete_payload['eventId']
    
    try:
        BebanjoPayloadSchema.model_validate(incomplete_payload)
        print("❌ Should have failed validation for missing eventId")
        return False
    except ValidationError as e:
        print("✅ Correctly caught missing eventId:")
        for error in e.errors():
            if 'event_id' in error['loc'] or 'eventId' in str(error['input']):
                print(f"   - Field: {error['loc']}, Message: {error['msg']}")
    
    # Test missing network
    incomplete_payload = bebanjo_payload.copy()
    del incomplete_payload['network']
    
    try:
        BebanjoPayloadSchema.model_validate(incomplete_payload)
        print("❌ Should have failed validation for missing network")
        return False
    except ValidationError as e:
        print("✅ Correctly caught missing network:")
        for error in e.errors():
            if 'network' in error['loc']:
                print(f"   - Field: {error['loc']}, Message: {error['msg']}")
    
    # Test missing uniqueId (critical field)
    incomplete_payload = bebanjo_payload.copy()
    del incomplete_payload['uniqueId']
    
    try:
        BebanjoPayloadSchema.model_validate(incomplete_payload)
        print("❌ Should have failed validation for missing uniqueId")
        return False
    except ValidationError as e:
        print("✅ Correctly caught missing uniqueId:")
        for error in e.errors():
            if 'unique_id' in error['loc'] or 'uniqueId' in str(error['input']):
                print(f"   - Field: {error['loc']}, Message: {error['msg']}")
    
    return True

def test_type_validation():
    """Test that incorrect types are caught."""
    print("\nTesting type validation...")
    
    # Test invalid eventId type (should be int)
    invalid_payload = bebanjo_payload.copy()
    invalid_payload['eventId'] = "not_an_integer"
    
    try:
        BebanjoPayloadSchema.model_validate(invalid_payload)
        print("❌ Should have failed validation for invalid eventId type")
        return False
    except ValidationError as e:
        print("✅ Correctly caught invalid eventId type:")
        for error in e.errors():
            if 'event_id' in error['loc'] or 'eventId' in str(error):
                print(f"   - Field: {error['loc']}, Message: {error['msg']}")
    
    # Test invalid seasonNumber type (should be int)
    invalid_payload = bebanjo_payload.copy()
    invalid_payload['seasonNumber'] = "not_an_integer"
    
    try:
        BebanjoPayloadSchema.model_validate(invalid_payload)
        print("❌ Should have failed validation for invalid seasonNumber type")
        return False
    except ValidationError as e:
        print("✅ Correctly caught invalid seasonNumber type:")
        for error in e.errors():
            if 'season_number' in error['loc'] or 'seasonNumber' in str(error):
                print(f"   - Field: {error['loc']}, Message: {error['msg']}")
    
    # Test invalid year type (should be int)
    invalid_payload = bebanjo_payload.copy()
    invalid_payload['year'] = "2017_as_string"
    
    try:
        BebanjoPayloadSchema.model_validate(invalid_payload)
        print("❌ Should have failed validation for invalid year type")
        return False
    except ValidationError as e:
        print("✅ Correctly caught invalid year type:")
        for error in e.errors():
            if 'year' in error['loc']:
                print(f"   - Field: {error['loc']}, Message: {error['msg']}")
    
    return True

def main():
    """Run all validation tests."""
    print("=" * 50)
    print("BEBANJO PAYLOAD VALIDATION TESTS")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    if test_valid_payload():
        tests_passed += 1
    
    if test_missing_required_fields():
        tests_passed += 1
    
    if test_type_validation():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All validation tests passed! The BebanjoPayloadSchema is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the validation model.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
