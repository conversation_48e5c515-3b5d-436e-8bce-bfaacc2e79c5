# Bebanjo Integration Tests

Este directorio contiene las pruebas de integración para la lambda de bebanjo que procesa archivos JSON desde S3 y los convierte usando RASCL.

## Estructura de Pruebas

### test_lambda_bebanjo.py
Pruebas principales de integración que cubren:

- **test_task_extract_data_mock**: Prueba la extracción de datos desde S3 mockeado
- **test_task_deserialize_data_mock**: Prueba la deserialización de JSON
- **test_task_parse_normal_series_mock**: Prueba el parsing usando RASCL con mocks
- **test_successful_bebanjo_integration_mocked_aws**: Prueba completa del lambda_handler con todos los servicios AWS mockeados
- **test_bebanjo_flow_integration**: Prueba del flujo completo de bebanjo
- **test_task_deserialize_data_error_handling**: Prueba manejo de errores en deserialización
- **test_task_parse_normal_series_error_handling**: Prueba manejo de errores en parsing
- **test_task_extract_data_file_not_found**: Prueba cuando el archivo no existe en S3

### test_bebanjo_payload_validation.py
Pruebas específicas de validación de payload que cubren:

- **test_bebanjo_payload_schema_validation**: Validación del schema con datos reales
- **test_bebanjo_payload_schema_validation_failure**: Casos de fallo en validación
- **test_parse_normal_series_from_bebanjo_success**: Función de parsing exitosa
- **test_parse_normal_series_from_bebanjo_type_error**: Manejo de errores de tipo
- **test_parse_normal_series_from_bebanjo_validation_error**: Errores de validación
- **test_parse_normal_series_from_bebanjo_rascl_exception**: Excepciones de RASCL
- **test_bebanjo_payload_case_conversion**: Conversión de casos de camelCase

## Servicios AWS Mockeados

Las pruebas utilizan `moto` para mockear los siguientes servicios AWS:

### S3
- **Bucket de origen**: `mds-bebanjo-schedules-sbx`
- **Bucket de destino**: `tacdev-reach-packages-sbx`
- **Archivos de prueba**: Ubicados en `/bebanjo/tests/files/`

### DynamoDB
- **Tabla de configuración**: `tacdev-event-config-sbx`
- **Tabla de estado**: `tacdev-logs-sbx`

### Secrets Manager
- **Secret**: `bebanjo-secrets-sbx`
- **Contenido**: 
  ```json
  {
    "auth": "mock_auth_token",
    "bearer": "mock_bearer_token", 
    "gracenote": "mock_gracenote_key"
  }
  ```

## Configuración de Pruebas

### Variables de Prueba (conftest.py)
```python
{
    "bucket_name": "mds-bebanjo-schedules-sbx",
    "destination_bucket": "tacdev-reach-packages-sbx", 
    "source_folder": "FF/2025/2",
    "file_path": "bebanjo/tests/files",
    "ENV": "sbx",
    "secret_key": "bebanjo-secrets-sbx",
    "filename_prefix": "bebanjo_",
}
```

### Fixtures Disponibles
- **aws_mock_test**: Mock de S3
- **dynamo_db_mock**: Mock de DynamoDB
- **secrets_manager_mock**: Mock de Secrets Manager
- **variables**: Variables de configuración de prueba
- **event**: Evento Lambda de prueba

## Archivos de Prueba

### TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json
Archivo JSON real de bebanjo que contiene:
- Metadatos de contenido VOD
- Información de programación
- Datos de assets y materiales
- Configuración de ventanas de licenciamiento

## Ejecución de Pruebas

```bash
# Ejecutar todas las pruebas de integración
cd bebanjo/
pytest tests/integration/

# Ejecutar pruebas específicas
pytest tests/integration/test_lambda_bebanjo.py::test_successful_bebanjo_integration_mocked_aws

# Ejecutar con verbose
pytest tests/integration/ -v

# Ejecutar con cobertura
pytest tests/integration/ --cov=src --cov-report=html
```

## Tasks Probadas

### TaskExtractData
- Extrae archivos JSON desde S3
- Utiliza el conector `extract` 
- Maneja errores de archivos no encontrados

### TaskDeserializeData  
- Deserializa JSON string a diccionario Python
- Valida formato JSON
- Maneja errores de parsing

### TaskParseNormalSeries
- Valida payload contra BebanjoPayloadSchema
- Invoca RASCL main function
- Convierte datos de bebanjo a formato ADI JSON
- Maneja configuración de secrets

## Mocks Utilizados

### RASCL
- `utils.rascl.parse_normal_series_from_MediaHub_JSON.main`: Función principal de RASCL
- `utils.rascl.parse_normal_series_from_MediaHub_JSON.config_parse`: Configuración de RASCL

### Configuración
- `src.bebanjo_lambda.settings_config`: Configuración de settings

## Validaciones Realizadas

1. **Extracción exitosa de S3**
2. **Deserialización correcta de JSON**
3. **Validación de schema de payload**
4. **Invocación correcta de RASCL**
5. **Actualización de tablas de estado en DynamoDB**
6. **Manejo de errores y excepciones**
7. **Configuración correcta de secrets**
8. **Flujo completo end-to-end**

## Ambiente de Prueba

Las pruebas están configuradas para simular el ambiente **sbx** (sandbox) con:
- Configuración de tablas DynamoDB específicas
- Buckets S3 de sandbox
- Secrets manager de sandbox
- Variables de entorno apropiadas
