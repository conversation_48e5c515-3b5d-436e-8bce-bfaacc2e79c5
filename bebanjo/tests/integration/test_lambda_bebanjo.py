import json
import os
from datetime import datetime
from unittest.mock import patch, MagicMock

import pytest
from src.bebanjo_lambda import lambda_handler
from src.bebanjo_flow import flow
from utils.extract import extract
from utils.schemas.lambdas import EventSchema
from utils.task_handlers import TaskExtractData, TaskDeserializeData, TaskParseNormalSeries


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
def test_task_extract_data_mock(aws_mock_test, variables):
    """Test TaskExtractData with mocked S3"""
    # Load test JSON file
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_json_content = f.read()

    # Upload test JSON to S3
    file_key = f"{variables['source_folder']}/TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json"
    aws_mock_test.put_object(
        Bucket=variables["bucket_name"],
        Key=file_key,
        Body=test_json_content,
    )

    # Create event and context
    event = EventSchema(**{
        "Records": [
            {
                "s3": {
                    "bucket": {"name": variables["bucket_name"]},
                    "object": {"key": file_key},
                }
            }
        ]
    })
    
    context = {
        "connector": extract,
    }

    # Test TaskExtractData
    task = TaskExtractData()
    task.prepare(event, context)
    result = task.perform_task()

    # Verify result
    assert result is not None
    assert isinstance(result, str)
    assert "eventId" in result
    assert "uniqueId" in result


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
def test_task_deserialize_data_mock(aws_mock_test, variables):
    """Test TaskDeserializeData with mocked data"""
    # Load test JSON file
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_json_content = f.read()

    # Test TaskDeserializeData
    task = TaskDeserializeData()
    result = task.perform_task(previous=test_json_content)

    # Verify result
    assert result is not None
    assert isinstance(result, dict)
    assert "eventId" in result
    assert "uniqueId" in result
    assert result["uniqueId"] == "mass0000000013626848"
    assert result["network"] == "FF"


@patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.main')
def test_task_parse_normal_series_mock(mock_rascl_main, variables):
    """Test TaskParseNormalSeries with mocked RASCL"""
    # Load test JSON file and parse it
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_data = json.load(f)

    # Mock RASCL main function to return expected ADI JSON
    mock_adi_json = {
        "adi": {
            "metadata": {
                "title": test_data.get("vodTitle", ""),
                "provider": test_data.get("provider", ""),
            }
        }
    }
    mock_rascl_main.return_value = mock_adi_json

    # Mock secrets
    mock_secrets = {
        "auth": "mock_auth_token",
        "bearer": "mock_bearer_token",
        "gracenote": "mock_gracenote_key"
    }

    # Test TaskParseNormalSeries
    task = TaskParseNormalSeries()
    event = EventSchema(**{
        "Records": [
            {
                "s3": {
                    "bucket": {"name": variables["bucket_name"]},
                    "object": {"key": "test.json"},
                }
            }
        ]
    })
    context = {"secrets": mock_secrets}
    task.prepare(event, context)
    result = task.perform_task(previous=test_data)

    # Verify result
    assert result is not None
    assert isinstance(result, dict)
    assert "adi" in result
    mock_rascl_main.assert_called_once()


@pytest.mark.parametrize("dynamo_db_mock", ["dynamodb"], indirect=True)
@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
@pytest.mark.parametrize("secrets_manager_mock", ["secretsmanager"], indirect=True)
@patch("src.bebanjo_lambda.settings_config")
@patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.main')
def test_successful_bebanjo_integration_mocked_aws(
    mock_rascl_main, mock_settings_config, aws_mock_test, dynamo_db_mock, secrets_manager_mock, variables
):
    """Test complete bebanjo lambda integration with mocked AWS services"""
    # Configure the mock for settings_config
    mock_settings_config.TACDEV_EVENT_CONFIG = f"tacdev-event-config-{variables['ENV']}"

    # Load test JSON file
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_json_content = f.read()
        test_data = json.loads(test_json_content)

    # Mock DynamoDB tables
    config_table_name = f"tacdev-event-config-{variables['ENV']}"
    status_table_name = f"tacdev-logs-{variables['ENV']}"

    # Create mock config table
    dynamo_db_mock.create_table(
        TableName=config_table_name,
        KeySchema=[{"AttributeName": "id", "KeyType": "HASH"}],
        AttributeDefinitions=[{"AttributeName": "id", "AttributeType": "N"}],
        BillingMode="PAY_PER_REQUEST",
    )

    # Create mock status table
    dynamo_db_mock.create_table(
        TableName=status_table_name,
        KeySchema=[{"AttributeName": "client_id", "KeyType": "HASH"}],
        AttributeDefinitions=[{"AttributeName": "client_id", "AttributeType": "S"}],
        BillingMode="PAY_PER_REQUEST",
    )

    # Define bebanjo config data
    current_time = datetime.now().isoformat()
    bebanjo_config_data = {
        "project_type": {"S": "bebanjo"},
        "landing_bucket": {"S": variables["bucket_name"]},
        "secret_key": {"S": variables["secret_key"]},
        "status_table_name": {"S": status_table_name},
        "filename_prefix": {"S": variables["filename_prefix"]},
        "destination_bucket": {"S": variables["destination_bucket"]},
    }

    # Put config data into mock table
    dynamo_db_mock.put_item(
        TableName=config_table_name,
        Item={
            "id": {"N": "1"},
            "event_project_name": {"S": "bebanjo"},
            "configuration_rules": {
                "M": {
                    "config": {"M": bebanjo_config_data},
                    "rules": {"M": {"mapping_rules_keys": {"L": []}}},
                }
            },
            "frequency": {"S": "online"},
            "time": {"S": "now"},
            "retries": {"N": "0"},
            "created_at": {"S": current_time},
            "updated_at": {"S": current_time},
            "deleted_at": {"S": current_time},
            "status": {"S": "active"},
            "description": {"S": "Bebanjo config for integration test"},
        },
    )

    # Upload test JSON to S3
    file_key = f"{variables['source_folder']}/TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json"
    aws_mock_test.put_object(
        Bucket=variables["bucket_name"],
        Key=file_key,
        Body=test_json_content,
    )

    # Mock RASCL main function
    mock_adi_json = {
        "adi": {
            "metadata": {
                "title": test_data.get("vodTitle", ""),
                "provider": test_data.get("provider", ""),
                "uniqueId": test_data.get("uniqueId", ""),
            }
        }
    }
    mock_rascl_main.return_value = mock_adi_json

    # Create test event
    event = {
        "Records": [
            {
                "s3": {
                    "bucket": {"name": variables["bucket_name"]},
                    "object": {"key": file_key},
                }
            }
        ]
    }

    # Execute lambda
    response = lambda_handler(event, None)

    # Verify response
    assert response["statusCode"] == 200
    assert "Package Successfully created into reach" in response["body"]

    # Verify status table was updated
    status_response = dynamo_db_mock.get_item(
        TableName=status_table_name,
        Key={"client_id": {"S": file_key}},
    )
    assert status_response["Item"]["process_status"]["S"] == "Completed"

    # Verify RASCL was called
    mock_rascl_main.assert_called_once()


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
def test_bebanjo_flow_integration(aws_mock_test, variables):
    """Test bebanjo flow integration with mocked services"""
    # Load test JSON file
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_json_content = f.read()

    # Upload test JSON to S3
    file_key = f"{variables['source_folder']}/TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json"
    aws_mock_test.put_object(
        Bucket=variables["bucket_name"],
        Key=file_key,
        Body=test_json_content,
    )

    # Create event and global variables
    event = EventSchema(**{
        "Records": [
            {
                "s3": {
                    "bucket": {"name": variables["bucket_name"]},
                    "object": {"key": file_key},
                }
            }
        ]
    })

    global_variables = {
        "landing_bucket": variables["bucket_name"],
        "destination_bucket": variables["destination_bucket"],
        "file_name": file_key,
        "secrets": {
            "auth": "mock_auth_token",
            "bearer": "mock_bearer_token",
            "gracenote": "mock_gracenote_key"
        },
        "mapping_rules": [],
    }

    # Mock RASCL main function
    with patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.main') as mock_rascl_main:
        mock_adi_json = {
            "adi": {
                "metadata": {
                    "title": "415390_T1_11-09B_title",
                    "provider": "Freeform",
                }
            }
        }
        mock_rascl_main.return_value = mock_adi_json

        # Execute flow
        result = flow(event, global_variables)

        # Verify result
        assert result is not None
        assert result["statusCode"] == 200
        assert "Package Successfully created into reach" in result["body"]
        mock_rascl_main.assert_called_once()


def test_task_deserialize_data_error_handling():
    """Test TaskDeserializeData error handling"""
    task = TaskDeserializeData()

    # Test with None input
    with pytest.raises(ValueError, match="No data provided to deserialize"):
        task.perform_task(previous=None)

    # Test with invalid JSON
    with pytest.raises(json.JSONDecodeError):
        task.perform_task(previous="invalid json {")


def test_task_parse_normal_series_error_handling():
    """Test TaskParseNormalSeries error handling"""
    task = TaskParseNormalSeries()

    # Test with non-dict input
    with pytest.raises(TypeError, match="Expected 'prev' to be a dict"):
        task.perform_task(previous="not a dict")


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
def test_task_extract_data_file_not_found(aws_mock_test, variables):
    """Test TaskExtractData when file doesn't exist in S3"""
    # Create event for non-existent file
    event = EventSchema(**{
        "Records": [
            {
                "s3": {
                    "bucket": {"name": variables["bucket_name"]},
                    "object": {"key": "non-existent-file.json"},
                }
            }
        ]
    })

    context = {
        "connector": extract,
    }

    # Test TaskExtractData with non-existent file
    task = TaskExtractData()
    task.prepare(event, context)

    # This should raise an exception when trying to extract non-existent file
    with pytest.raises(Exception):
        task.perform_task()
