import json
import os
from unittest.mock import patch

import pytest
from pydantic import ValidationError
from utils.schemas.payloads import BebanjoPayloadSchema
from utils.tasks.data_processing import parse_normal_series_from_bebanjo


def test_bebanjo_payload_schema_validation(variables):
    """Test BebanjoPayloadSchema validation with real test data"""
    # Load test JSON file
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_data = json.load(f)

    # Test validation with original data
    # The schema should validate the required 'network' field
    assert "network" in test_data
    assert test_data["network"] == "FF"
    
    # Create a minimal payload that should pass validation
    minimal_payload = {
        "network": test_data["network"]
    }
    
    # This should not raise an exception
    validated_payload = BebanjoPayloadSchema.model_validate(minimal_payload)
    assert validated_payload.network == "FF"


def test_bebanjo_payload_schema_validation_failure():
    """Test BebanjoPayloadSchema validation failure cases"""
    # Test with missing required field
    invalid_payload = {
        "eventId": 123,
        "uniqueId": "test123"
        # Missing 'network' field
    }
    
    with pytest.raises(ValidationError):
        BebanjoPayloadSchema.model_validate(invalid_payload)


@patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.main')
@patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.config_parse')
def test_parse_normal_series_from_bebanjo_success(mock_config_parse, mock_rascl_main, variables):
    """Test parse_normal_series_from_bebanjo function with valid data"""
    # Load test JSON file
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_data = json.load(f)

    # Mock the config_parse function
    mock_config = {"mock": "config"}
    mock_config_parse.return_value = mock_config

    # Mock RASCL main function
    mock_adi_json = {
        "adi": {
            "metadata": {
                "title": test_data.get("vodTitle", ""),
                "provider": test_data.get("provider", ""),
            }
        }
    }
    mock_rascl_main.return_value = mock_adi_json

    # Mock secrets
    mock_settings = {
        "auth": "mock_auth_token",
        "bearer": "mock_bearer_token",
        "gracenote": "mock_gracenote_key"
    }

    # Test the function
    result = parse_normal_series_from_bebanjo(test_data, mock_settings)

    # Verify result
    assert result is not None
    assert isinstance(result, dict)
    assert "adi" in result
    
    # Verify mocks were called correctly
    mock_config_parse.assert_called_once_with(mock_settings)
    mock_rascl_main.assert_called_once_with(test_data, mock_config)


def test_parse_normal_series_from_bebanjo_type_error():
    """Test parse_normal_series_from_bebanjo with invalid input type"""
    # Test with non-dict input
    with pytest.raises(TypeError, match="Expected 'prev' to be a dict"):
        parse_normal_series_from_bebanjo("not a dict")


@patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.main')
@patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.config_parse')
def test_parse_normal_series_from_bebanjo_validation_error(mock_config_parse, mock_rascl_main):
    """Test parse_normal_series_from_bebanjo with validation error"""
    # Create invalid payload (missing required network field)
    invalid_data = {
        "eventId": 123,
        "uniqueId": "test123"
        # Missing 'network' field
    }

    # Mock the config_parse function
    mock_config = {"mock": "config"}
    mock_config_parse.return_value = mock_config

    # This should raise a ValidationError due to missing required field
    with pytest.raises(ValidationError):
        parse_normal_series_from_bebanjo(invalid_data, {})


@patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.main')
@patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.config_parse')
def test_parse_normal_series_from_bebanjo_rascl_exception(mock_config_parse, mock_rascl_main, variables):
    """Test parse_normal_series_from_bebanjo when RASCL raises exception"""
    # Load test JSON file
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_data = json.load(f)

    # Mock the config_parse function
    mock_config = {"mock": "config"}
    mock_config_parse.return_value = mock_config

    # Mock RASCL main function to raise an exception
    mock_rascl_main.side_effect = Exception("RASCL processing error")

    # This should propagate the RASCL exception
    with pytest.raises(Exception, match="RASCL processing error"):
        parse_normal_series_from_bebanjo(test_data, {})


def test_bebanjo_payload_case_conversion(variables):
    """Test that BebanjoPayloadSchema handles case conversion correctly"""
    # Load test JSON file
    test_file_path = os.path.join(variables["file_path"], "TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json")
    with open(test_file_path, 'r') as f:
        test_data = json.load(f)

    # The test data has camelCase keys, but our schema expects lowercase
    # The parse_normal_series_from_bebanjo function should handle this conversion
    
    # Test that the original data has camelCase keys
    assert "eventId" in test_data  # camelCase
    assert "uniqueId" in test_data  # camelCase
    
    # Test that the function can handle the case conversion internally
    # This is tested indirectly through the parse_normal_series_from_bebanjo function
    with patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.main') as mock_rascl_main:
        with patch('utils.rascl.parse_normal_series_from_MediaHub_JSON.config_parse') as mock_config_parse:
            mock_config_parse.return_value = {}
            mock_rascl_main.return_value = {"adi": {}}
            
            # This should not raise a validation error
            result = parse_normal_series_from_bebanjo(test_data, {})
            assert result is not None
