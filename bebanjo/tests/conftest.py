"""bebanjo tests configuration
"""

import os
import json

import boto3
import pytest
from moto import mock_aws
from utils import config


@pytest.fixture
def variables():
    """Test variables for bebanjo integration tests"""
    project_base_path = os.path.dirname(os.path.abspath(__file__))
    return {
        "bucket_name": "mds-bebanjo-schedules-sbx",
        "destination_bucket": "tacdev-reach-packages-sbx",
        "source_folder": "FF/2025/2",
        "file_path": f"{project_base_path}/files",
        "ENV": "sbx",
        "secret_key": "bebanjo-secrets-sbx",
        "filename_prefix": "bebanjo_",
    }


@pytest.fixture
def aws_mock_test(request: str, variables):
    """Mock AWS S3 service for testing"""
    service = request.param
    with mock_aws():
        client = boto3.client(service, region_name=config.REGION_NAME)

        if service == "s3":
            # Set up S3 buckets for testing
            client.create_bucket(
                Bucket=variables["bucket_name"],
                CreateBucketConfiguration={"LocationConstraint": config.REGION_NAME},
            )
            client.create_bucket(
                Bucket=variables["destination_bucket"],
                CreateBucketConfiguration={"LocationConstraint": config.REGION_NAME},
            )

        yield client


@pytest.fixture
def dynamo_db_mock(request: str, variables):
    """Mock AWS DynamoDB service for testing"""
    service = request.param
    with mock_aws():
        client = boto3.client(service, region_name=config.REGION_NAME)
        yield client


@pytest.fixture
def secrets_manager_mock(request: str, variables):
    """Mock AWS Secrets Manager service for testing"""
    service = request.param
    with mock_aws():
        client = boto3.client(service, region_name=config.REGION_NAME)

        # Create mock secrets for bebanjo
        bebanjo_secrets = {
            "auth": "mock_auth_token",
            "bearer": "mock_bearer_token",
            "gracenote": "mock_gracenote_key"
        }

        client.create_secret(
            Name=variables["secret_key"],
            SecretString=json.dumps(bebanjo_secrets)
        )

        yield client


@pytest.fixture
def event(variables):
    """Mock AWS Lambda event for bebanjo testing"""
    return {
        "Records": [
            {
                "s3": {
                    "bucket": {"name": variables["bucket_name"]},
                    "object": {"key": f"{variables['source_folder']}/TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json"},
                }
            }
        ]
    }
