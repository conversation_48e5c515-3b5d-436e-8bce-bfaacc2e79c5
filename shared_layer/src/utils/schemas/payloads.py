from datetime import datetime
from typing import Any, List, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field


class WonderlandSidecarSchema(BaseModel):
    """Schema for Wonderland sidecar JSON payload."""

    model_config = ConfigDict(extra="ignore")

    version: int
    sidecar_id: str
    asset_version: str
    asset_format: Literal["single_file", "multi_file"]
    asset_id: str
    reach_package_id: str


class BebanjoPayloadSchema(BaseModel):
    """Schema for Bebanjo payload from S3 bucket.
    
    Validates all required fields needed by the main function to construct the adi_json object.
    Based on analysis of parse_normal_series_from_MediaHub_JSON.py main function.
    """

    # model_config = ConfigDict(extra="ignore", str_strip_whitespace=True)

    # # Core identifiers - Required fields
    # event_id: int = Field(alias="eventId", description="Event identifier")
    # unique_id: str = Field(alias="uniqueId", description="Unique identifier for the content")
    # traffic_code: str = Field(alias="trafficCode", description="Traffic code identifier")
    
    # # Content metadata - Required for content identification
    # change_type: str = Field(alias="changeType", description="Type of change (A, U, D)")
    network: str = Field(alias="network", description="Network identifier")
    # platform_name: str = Field(alias="platformName", description="Platform name")
    # provider: str = Field(alias="provider", description="Content provider")
    
    # # Title information - Required for content naming
    # vod_title: str = Field(alias="vodTitle", description="VOD title")
    # vod_short_title: str = Field(alias="vodShortTitle", description="VOD short title")
    # show_title: str = Field(alias="showTitle", description="Show title")
    # episode_name: str = Field(alias="episodeName", description="Episode name")
    
    # # Episode/Season information - Required for series content
    # season_number: int = Field(alias="seasonNumber", description="Season number")
    # episode_number: int = Field(alias="episodeNumber", description="Episode number")
    # episode_id: str = Field(alias="episodeID", description="Episode identifier")
    
    # # Content classification - Required for categorization
    # show_type: str = Field(alias="showType", description="Type of show (TVShow, Movie, etc.)")
    # asset_type: str = Field(alias="assetType", description="Asset type (VOD, etc.)")
    # rating: str = Field(description="Content rating")
    # genres: str = Field(description="Content genres")
    
    # # Technical metadata - Required for processing
    # display_run_time: str = Field(alias="displayRunTime", description="Display runtime")
    # year: int = Field(description="Content year")
    # closed_captioning: str = Field(alias="closedCaptioning", description="Closed captioning availability")
    
    # # Asset identifiers - Required for asset management
    # asset_name: str = Field(alias="assetName", description="Asset name")
    # xml_file_name: str = Field(alias="xmlFileName", description="XML file name")
    # video_file_name: str = Field(alias="videoFileName", description="Video file name")
    
    # # S3 and file location - Required for file processing
    # s3_file_location: str = Field(alias="s3FileLocation", description="S3 file location")
    
    # # Package and title asset information - Required for asset construction
    # asset_id_package: str = Field(alias="assetIdPackage", description="Package asset ID")
    # asset_name_package: str = Field(alias="assetNamePackage", description="Package asset name")
    # asset_id_title: str = Field(alias="assetIdTitle", description="Title asset ID")
    # asset_name_title: str = Field(alias="assetNameTitle", description="Title asset name")
    
    # # Series information - Required for series content
    # series_name: str = Field(alias="seriesName", description="Series name")
    # provider_id: str = Field(alias="providerId", description="Provider identifier")
    
    # # Descriptions - Required for content metadata
    # description_package: str = Field(alias="descriptionPackage", description="Package description")
    # description_title: str = Field(alias="descriptionTitle", description="Title description")
    # summary_short: str = Field(alias="summaryShort", description="Short summary")
    # one_line_description: str = Field(alias="oneLineDescription", description="One line description")
    
    # # Product and billing information - Required for business logic
    # product: str = Field(description="Product identifier")
    # title_brief: str = Field(alias="titleBrief", description="Brief title")
    # billing_id: str = Field(alias="billingID", description="Billing identifier")
    # provider_qa_contact: str = Field(alias="providerQaContact", description="Provider QA contact")
    
    # # Movie asset information - Required for movie content
    # asset_id_movie: str = Field(alias="assetIdMovie", description="Movie asset ID")
    # asset_name_movie: str = Field(alias="assetNameMovie", description="Movie asset name")
    # description_movie: str = Field(alias="descriptionMovie", description="Movie description")
    
    # # Technical specifications - Required for technical processing
    # hd_content: str = Field(alias="hdContent", description="HD content flag")
    # audio_type: str = Field(alias="audioType", description="Audio type")
    # languages: str = Field(description="Content languages")
    
    # # Poster/artwork information - Required for visual assets
    # asset_id_post: str = Field(alias="assetIdPost", description="Poster asset ID")
    # asset_name_post: str = Field(alias="assetNamePost", description="Poster asset name")
    # description_post: str = Field(alias="descriptionPost", description="Poster description")
    # content_file_size_image: int = Field(alias="contentFileSizeImage", description="Image file size")
    # content_check_sum_image: str = Field(alias="contentCheckSumImage", description="Image checksum")
    
    # # Show and content codes - Required for content identification
    # show_code: str = Field(alias="showCode", description="Show code")
    # placing_id: int = Field(alias="placingId", description="Placing identifier")
    
    # # Date/time fields - Required for scheduling and licensing
    # air_date: str = Field(alias="airDate", description="Air date")
    # create_date: str = Field(alias="createDate", description="Create date")
    # original_airdate: str = Field(alias="originalAirdate", description="Original air date")
    # licensing_window_start: str = Field(alias="licensingWindowStart", description="Licensing window start")
    # licensing_window_end: str = Field(alias="licensingWindowEnd", description="Licensing window end")
    
    # # Optional fields that may be null or empty
    # transaction_time: Optional[str] = Field(default=None, alias="transactionTime")
    # vod4_window_start: Optional[str] = Field(default=None, alias="vod4WindowStart")
    # vod4_window_end: Optional[str] = Field(default=None, alias="vod4WindowEnd")
    # vod8_window_start: Optional[str] = Field(default=None, alias="vod8WindowStart")
    # vod8_window_end: Optional[str] = Field(default=None, alias="vod8WindowEnd")
    # c3_window_start: Optional[str] = Field(default=None, alias="c3WindowStart")
    # c3_window_end: Optional[str] = Field(default=None, alias="c3WindowEnd")
    # c7_window_start: Optional[str] = Field(default=None, alias="c7WindowStart")
    # c7_window_end: Optional[str] = Field(default=None, alias="c7WindowEnd")
    # clean_start: Optional[str] = Field(default=None, alias="cleanStart")
    # clean_end: Optional[str] = Field(default=None, alias="cleanEnd")
    
    # episode_vod_title: Optional[str] = Field(default=None, alias="episodeVodTitle")
    # episode_vod_short_title: Optional[str] = Field(default=None, alias="episodeVodShortTitle")
    # episode_rating: Optional[str] = Field(default="", alias="episodeRating")
    # movie_rating: Optional[str] = Field(default=None, alias="movieRating")
    
    # actors: Optional[str] = Field(default="", description="Actors list")
    # radar_product_id: Optional[str] = Field(default=None, alias="radarProductId")
    
    # cmc_categories: Optional[List[str]] = Field(default_factory=list, alias="cmcCategories")
    
    # episode_original_air_date: Optional[bool] = Field(default=None, alias="episodeOriginalAirDate")
    # schedule_materials: Optional[str] = Field(default=None, alias="scheduleMaterials")
    
    # artworks_url: Optional[str] = Field(default=None, alias="artworksUrl")
    # artworks_type: Optional[str] = Field(default=None, alias="artworksType")
    # artworks_file_name: Optional[str] = Field(default=None, alias="artworksFileName")
    
    # published_date_time: Optional[str] = Field(default=None, alias="publishedDateTime")
    
    # series_id: Optional[str] = Field(default="", alias="seriesId")
    
    # trick_modes_restricted: Optional[str] = Field(default=None, alias="trickModesRestricted")
    
    # season_premiere: Optional[str] = Field(default=None, alias="seasonPremiere")
    # season_finale: Optional[str] = Field(default=None, alias="seasonFinale")
    # title_sort_name: Optional[str] = Field(default=None, alias="titleSortName")
    # country_of_origin: Optional[str] = Field(default=None, alias="countryOfOrigin")
    # authority: Optional[str] = Field(default=None, alias="authority")
    # content_rating_descriptors: Optional[str] = Field(default="", alias="contentRatingDescriptors")
