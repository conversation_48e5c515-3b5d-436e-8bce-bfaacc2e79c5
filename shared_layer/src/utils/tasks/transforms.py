import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import yaml
from yaml import safe_load

from ..config import EN<PERSON>
from ..DynamoUtils import DynamoDBClient
from ..load import load
from ..LoggerService import logger_service
from ..mapping.config_obj import ConfigObj
from ..mapping.mapping_dict import MappingDict
from ..SecretsManagerUtils import SecretsManagerUtils


def extract_nested_element_by_path(path: str, data: Dict) -> Any:
    """Extract a nested element from a dictionary using a dot-notated path.

    Args:
        path: Dot-notated string path to the element (e.g., "parent.child.target")
        data: Dictionary to extract the element from

    Returns:
        Any: The element at the specified path or None if not found
    """
    splitted_path = path.split(".")
    located_position = data
    for key in splitted_path:
        located_position = located_position.get(key)
    return located_position


def extract_value_by_key_validation(data: Any, validation: Dict[str, str]) -> Any:
    """Recursively search for and extract a value from a data structure using key validation.

    Args:
        data: Dictionary or list to search within
        validation: Dictionary containing key_name and validation_value to match

    Returns:
        Any: The extracted value if found, None otherwise

    Raises:
        ValueError: If no valid value is found when a matching key/value is located
    """
    if isinstance(data, dict):
        for key, value in data.items():
            if (
                key == validation["key_name"]
                and value == validation["validation_value"]
            ):
                extracted_value = data.get("value") or data.get("@Value")
                if not extracted_value:  # Checks if None or empty string
                    logger_service.error("No valid value found for Data: %s", data)
                    raise ValueError(f"No valid value found for Data: {data}")
                return extracted_value

            result = extract_value_by_key_validation(value, validation)
            if result is not None:
                return result
    elif isinstance(data, list):
        for item in data:
            result = extract_value_by_key_validation(item, validation)
            if result is not None:
                return result
    return None


def replace_key_in_nested_structure(
    prev: Dict, path: str, key_to_replace: str, global_variables: Optional[Dict] = None
) -> Dict:
    """Replace a key in a nested dictionary structure at the specified path.

    Args:
        prev: The dictionary to modify
        path: Dot-notated path to the key location
        key_to_replace: New key name to replace the existing key
        global_variables: Optional dictionary of global variables

    Returns:
        Dict: The modified dictionary with the key replaced

    Note:
        This function does not support objects or dictionaries inside arrays/lists
    """
    keys = path.split(".")
    current = prev

    for key in keys[:-1]:
        current = current.get(key, {})

    last_key = keys[-1]

    if key_to_replace:
        actual_value = current[last_key]
        current[key_to_replace] = actual_value
        del current[last_key]
    return prev


def replace_value_with_split_components(
    prev: Dict,
    validation: Dict,
    value_to_split: str,
    target_path: Optional[str] = None,
    split_char: str = "|",
    splitted_value: Optional[List] = None,
    global_variables: Optional[Dict] = None,
) -> Dict:
    """Replace a value with components split from a delimited string.

    Args:
        prev: Dictionary to modify
        validation: Dictionary with key_name and validation_value for locating the target
        value_to_split: Key name of the value to be split
        target_path: Path where the split values should be applied
        split_char: Character used to split the string (default: "|")
        splitted_value: Optional pre-split values list
        global_variables: Optional dictionary of global variables

    Returns:
        Dict: The modified dictionary with split values applied
    """
    if not splitted_value:
        splitted_value = extract_value_by_key_validation(prev, validation).split(
            split_char
        )

    if splitted_value:
        prev = update_attribute_value_by_validation(
            prev=prev,
            value_to_replace=value_to_split,
            new_value=splitted_value.pop(0),
            validation=validation,
        )
        target_position = extract_nested_element_by_path(target_path, prev)
        for item in splitted_value:
            target_position.append({"@App": "MOD", "@Name": "Category", "@Value": item})
    return prev


def update_attribute_value_by_validation(
    prev: Union[Dict, List],
    validation: Optional[Dict] = None,
    value_to_replace: Optional[str] = None,
    new_value: Optional[Any] = None,
    different_source_validation: Optional[Dict] = None,
    global_variables: Optional[Dict] = None,
) -> Union[Dict, List]:
    """Update an attribute value in a dictionary based on validation criteria.

    Args:
        prev: Dictionary or list to modify
        validation: Dictionary with key_name and validation_value for locating the target
        value_to_replace: Key name of the value to be replaced
        new_value: New value to set
        different_source_validation: Optional validation to extract value from a different source
        global_variables: Optional dictionary of global variables

    Returns:
        Union[Dict, List]: The modified dictionary or list with updated values
    """
    if different_source_validation:
        new_value = extract_value_by_key_validation(
            global_variables.get("original_source"), different_source_validation
        )
    if isinstance(prev, dict):
        for key, value in prev.items():
            if key == validation.get("key_name") and not validation.get(
                "validation_value"
            ):
                if value_to_replace in prev and new_value is not None:
                    prev[value_to_replace] = new_value
            elif key == validation.get("key_name") and value == validation.get(
                "validation_value"
            ):
                if value_to_replace in prev and new_value is not None:
                    prev[value_to_replace] = new_value
            else:
                update_attribute_value_by_validation(
                    value, validation, value_to_replace, new_value
                )
    elif isinstance(prev, list):
        for item in prev:
            update_attribute_value_by_validation(
                item, validation, value_to_replace, new_value
            )
    return prev


def convert_json_to_yaml(prev: Union[str, Dict], sort_keys: bool = False) -> str:
    """Convert JSON string or dict to YAML format.

    Args:
        prev: JSON string or dict to convert
        sort_keys: Whether to sort keys in YAML output. Defaults to False.

    Returns:
        str: YAML formatted string

    Raises:
        ValueError: If input JSON is invalid or conversion fails
    """
    try:
        # If prev is already a dict, use it directly
        if isinstance(prev, dict):
            data = prev
        else:
            # Parse JSON string to dict
            data = json.loads(prev)

        # Convert to YAML
        yaml_content = yaml.dump(
            data, default_flow_style=False, sort_keys=sort_keys, allow_unicode=True
        )
        return yaml_content

    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON format: {str(e)}")
    except Exception as e:
        raise ValueError(f"Error converting to YAML: {str(e)}")


def add_object_data_at_path(
    filter_key: str, keys_to_follow: List[str], data_dict: Any, new_obj: Dict
) -> Any:
    """Add or merge object data at a specified path if a filter key is present.

    Args:
        filter_key: Key to check for in the dictionary
        keys_to_follow: List of keys defining the path to follow
        data_dict: Dictionary or list to modify
        new_obj: New object data to merge

    Returns:
        Any: The modified data structure with the new object data added
    """
    # Base case: if no more keys to follow, check for filter_key and modify
    if not keys_to_follow:
        if isinstance(data_dict, dict) and filter_key in data_dict:
            # Merge new_obj at this level
            data_dict.update(new_obj)
        return data_dict

    key = keys_to_follow[0]  # Get the current key to follow

    if isinstance(data_dict, dict):
        if key in data_dict:
            # Recursively call function for the next level
            data_dict[key] = add_object_data_at_path(
                filter_key, keys_to_follow[1:], data_dict[key], new_obj
            )

    elif isinstance(data_dict, list):
        for index, item in enumerate(data_dict):
            if isinstance(item, dict):
                # Apply recursion for each dictionary inside the list
                data_dict[index] = add_object_data_at_path(
                    filter_key, keys_to_follow, item, new_obj
                )

    return data_dict


def update_wonderland_status_in_dynamo(prev: Dict) -> Dict:
    """Extract metadata and update the Wonderland status in DynamoDB.

    Args:
        prev: Dictionary containing sidecar_id and attribute_name

    Returns:
        Dict: The input dictionary with reach_package_id removed

    Side Effects:
        Creates a new record in the DynamoDB status table with a status of "wl_in_progress"
    """
    sidecar_id = prev.get("sidecar_id", "")
    reach_package_id = prev.pop("reach_package_id", "")

    # Fill the "Reach to Wonderland" status DynamoDB table
    table_name = f"tacdev-reach-to-wonderland-{ENV}"
    db_client = DynamoDBClient(table_name)
    current_timestamp = datetime.now().isoformat()
    status_item = {
        "sidecar_id": sidecar_id,  # Partition key
        "wonderland_id": "",
        "reach_package_id": reach_package_id,
        "created_at": current_timestamp,
        "updated_at": current_timestamp,
        "deleted_at": "",  # Empty indicates not deleted
        "status": "wl_in_progress",
        "details": "The file has been uploaded. Ingestion is in progress",
    }
    db_client.create_item(status_item)

    return prev

def update_dynamo_record_status(
    table_name: str, key: Dict, status: str, sidecar: str
) -> bool:
    """Update status and timestamp for a record in a DynamoDB table.

    Args:
        table_name: DynamoDB table name
        key: Key dictionary to identify the record
        status: New status value to set
        sidecar: Sidecar ID for logging purposes

    Returns:
        bool: True if update was successful, False otherwise

    Side Effects:
        Updates a record in DynamoDB with new status and current timestamp
    """
    try:
        current_timestamp = datetime.now().isoformat()  # Get current timestamp
        load(
            {
                "load": {
                    "task": "dynamo_upload",
                    "params": {
                        "table": table_name,
                        "key": key,
                        "method": "update_item",
                        # Include status and updated_at in the update
                        "value": {
                            "status": status,
                            "updated_at": current_timestamp,
                        },
                    },
                }
            }
        )
        logger_service.info(
            "Successfully updated status to %s for sidecar %s", status, sidecar
        )
        return True
    except Exception as update_error:
        logger_service.error("Failed to update status to %s: %s", status, update_error)
        return False


def update_dynamo_extract(table: str, key: dict, value: dict) -> bool:
    """Update a DynamoDB item using the extract method.

    Args:
        table: DynamoDB table name
        key: Key dictionary to identify the record
        value: Dictionary containing values to update

    Returns:
        bool: True if update was successful, False otherwise

    Side Effects:
        Updates a record in DynamoDB with the provided values
    """
    try:
        dynamo_update = {
            "task": "dynamo_extract",
            "params": {
                "table": table,
                "key": key,
                "value": value,
                "method": "update_item",
            },
        }

        params = dynamo_update.get("params", {})
        dynamo_client = DynamoDBClient(table)
        dynamo_client.update_item(params.get("key"), params.get("value"))
        logger_service.info(
            "Successfully updated table to: %s for key: %s and value: %s",
            table,
            key,
            value,
        )
        return True
    except Exception as update_error:
        logger_service.error(
            "Failed to update table to: %s for key: %s and value: %s",
            table,
            key,
            value,
        )
        return False


def remap_json_keys_recursively(
    json_input: Dict[str, Any], mapping_rules: List[Dict[str, str]]
) -> Dict[str, Any]:
    """Recursively replace keys in a dictionary based on mapping rules.

    Args:
        json_input: The input dictionary to modify
        mapping_rules: List of mapping rules with source_field_name and destination_field_name

    Returns:
        Dict[str, Any]: Dictionary with keys renamed according to rules at all nested levels

    Raises:
        ValueError: If no mapping rules are provided
        TypeError: If input is not a dictionary or list
    """
    if not mapping_rules:
        logger_service.error(
            "No mapping rules provided to map_json_keys. Returning original input."
        )
        error_message = "No mapping rules provided to map_json_keys"
        logger_service.error(f"{error_message}. Stopping execution.")
        raise ValueError(error_message)

    rules_map = {
        rule["source_field_name"]: rule["destination_field_name"]
        for rule in mapping_rules
        if "source_field_name" in rule and "destination_field_name" in rule
    }

    def recursive_map(item: Any) -> Any:
        if isinstance(item, dict):
            new_dict = {}
            for key, value in item.items():
                processed_value = recursive_map(value)
                new_key = rules_map.get(key, key)
                new_dict[new_key] = processed_value
            return new_dict
        elif isinstance(item, list):
            return [recursive_map(element) for element in item]
        else:
            return item

    if not isinstance(json_input, (dict, list)):
        raise TypeError("Input must be a dictionary or a list.")

    return recursive_map(json_input)
