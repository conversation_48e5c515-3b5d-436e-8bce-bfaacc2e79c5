import json
from typing import Any, Dict, Optional, Union

import xmltodict

from ..rascl.parse_normal_series_from_MediaHub_JSON import (
    config_parse,
)
from ..rascl.parse_normal_series_from_MediaHub_JSON import main as rascl_main
from ..schemas.payloads import BebanjoPayloadSchema


def convert_to_dict(
    prev: Any, source_format: str, global_variables: Optional[Dict[str, Any]] = None
) -> Any:
    """Convert data from a specific format to a Python dictionary.

    Args:
        prev: The data to be converted.
        source_format: The format of the input data ('xml', 'json', or other).
        global_variables: Additional variables that may be used during conversion.

    Returns:
        dict: The converted data as a dictionary if source_format is 'xml' or 'json',
              otherwise returns the original data.
    """
    if source_format == "xml":
        return xmltodict.parse(prev)
    elif source_format == "json":
        return json.loads(prev)
    return prev


def convert_from_dict(
    prev: Any, target_format: str, global_variables: Optional[Dict[str, Any]] = None
) -> Any:
    """Convert a Python dictionary to a specific format.

    Args:
        prev: The dictionary data to be converted.
        target_format: The format to convert to ('xml', 'json', or other).
        global_variables: Additional variables that may be used during conversion.

    Returns:
        Union[str, Any]: The converted data as a string if target_format is 'xml' or 'json',
                        otherwise returns the original data.
    """
    if target_format == "xml":
        return xmltodict.unparse(prev, pretty=True)
    elif target_format == "json":
        return json.dumps(prev)
    return prev


def download_s3_object(
    prev: Optional[dict] = None, key: str = "", connector: Any = None, bucket: str = ""
) -> bytes:
    """Download an object from an S3 bucket.

    Args:
        prev: Previous task's output. Not used in this function but kept for task chaining.
        key: The S3 object key to download.
        connector: The function or method to execute the S3 operation.
        bucket: The name of the S3 bucket.

    Returns:
        bytes: The downloaded object's data.
    """
    task_def: dict[str, Any] = {
        "extract": {
            "task": "s3_extract",
            "params": {
                "key": key,
                "bucket": bucket,
            },
        }
    }
    result = connector(task_def)
    return result


def parse_normal_series_from_bebanjo(
    prev: dict[str, Any], settings: Optional[dict[str, Any]] = None
) -> dict[str, Any]:
    """Validate Bebanjo JSON and build ADI JSON via RASCL main.

    This function expects the output of TaskDeserializeData (a Python dict
    produced from the S3 JSON). It validates the payload against
    BebanjoPayloadSchema and, if valid, invokes the RASCL ``main`` method
    to construct the ``adi_json``.

    Args:
        prev: The deserialized Bebanjo JSON payload.
        settings: Optional configuration context containing a ``secrets``
            mapping required by RASCL (auth/bearer/gracenote).

    Returns:
        dict[str, Any]: The constructed ADI JSON.

    Raises:
        pydantic.ValidationError: If the payload does not match the schema.
        Exception: Any exception propagated from the RASCL parser.
    """
    if not isinstance(prev, dict):
        raise TypeError("Expected 'prev' to be a dict with the Bebanjo payload")
    # Attempt validation using original keys (exact match to aliases)
    fixed_payload = {k[:1].lower() + k[1:] if k and k[0].isupper() else k: v for k, v in prev.items()}
    _ = BebanjoPayloadSchema.model_validate(fixed_payload)
    

    # Prepare RASCL configuration
    settings = settings or {}
    local_config = config_parse(settings)

    # Build ADI JSON using the original payload (preserves original key casing)
    return rascl_main(prev, local_config)


def upload_to_s3(prev: Any, key: str, connector: Any, bucket: str) -> bytes:
    """Upload data to an S3 bucket.

    Args:
        prev: The data to upload to S3.
        key: The S3 object key where the data will be stored.
        connector: The function or method to execute the S3 operation.
        bucket: The name of the S3 bucket.

    Returns:
        bytes: Response from the S3 upload operation.
    """
    task_def: dict[str, Any] = {
        "load": {
            "task": "s3_upload",
            "params": {
                "Key": key,
                "Bucket": bucket,
                "Body": prev,
            },
        }
    }
    result = connector(task_def)
    return result


def retrieve_dynamodb_item(
    table_name: str, key: Any, method: str, connector: Any
) -> dict:
    """Retrieve an item from a DynamoDB table.

    Args:
        table_name: The name of the DynamoDB table.
        key: The key identifying the item to retrieve.
        method: The method to use for retrieval (e.g., 'get_item', 'query').
        connector: The function or method to execute the DynamoDB operation.

    Returns:
        dict: The retrieved item or query result from DynamoDB.
    """
    task_def: dict[str, Any] = {
        "extract": {
            "task": "dynamo_extract",
            "params": {"table": table_name, "key": key, "method": method},
        }
    }

    result = connector(task_def)
    return result
