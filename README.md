# Release-Based Branching & Deployment Workflow

## 🎯 Objective
This project implements a **release branch strategy** at the start of the sprint. Each new piece of software or feature starts from the **develop branch**, and at the start of the sprint, a new **release/x.y.z** branch is created, which will be the target for feature merges.

Deployments are **only triggered by the creation of a properly formatted tag**, pushed from the **release/* branch**.

## 📦 Branch Workflow
* **develop**: continuous integration branch, base for new features
* **release/x.y.z**: release branch created at the start of the sprint. Validated features are merged here
* **main**: production branch

Each tag must follow this convention: **release/version-environment**

## General Process
* At the start of the sprint, **release/x.y.z** is created from **develop**
* Each feature is developed in **feature/* branches**, and upon completion, it is merged into **release/x.y.z**
* At the end of the sprint, the corresponding tag (release/x.y.z-sbx) is created to trigger the deployment to the desired environment

### Tags format
| Environment | Tag format           | Example              |
| -------     | -------------------- | -------------------- |
| Sandbox     | `release/x.y.z-sbx`  | `release/1.0.3-sbx`  |
| QA          | `release/x.y.z-qa`   | `release/1.0.3-qa`   |
| Production  | `release/x.y.z-prod` | `release/1.0.3-prod` |

## 🚀 Deployment Instructions
### 1. Create the release branch (at the start of the sprint)

```bash
    git checkout develop
    git pull origin develop
    git checkout -b release/1.0.0
    git push origin release/1.0.0
```

### 2. Merge features (during the sprint)

```bash
    # Make sure you're on the right branch
    git checkout release/1.0.0
    git merge feature/jira-ticket
    git push origin release/1.0.0
```

### 3. Create and upload the tag (when you want to display it)
```bash
    git checkout release/1.0.0
    git pull origin release/1.0.0
    git tag release/1.0.0-sbx
    git push origin release/1.0.0-sbx
```

### 4. Integrate changes from release/* to develop (post-release)
After creating the tag and completing validation or deployment, changes from the release/x.y.z branch must be integrated back into develop to keep it up to date.
```bash
    git checkout develop
    git pull origin develop
    git merge release/1.0.0
    git push origin develop
```

### 🔒 Tag Restrictions
### ✅ Tag protection
The release/* tags are protected in GitLab:
* Only authorized users can create them
* They cannot be overwritten accidentally
* Pipelines will ignore tags that don't match the expected pattern

### Automatic validation in CI/CD
* Pipelines are only triggered if:
  * The tag matches the allowed pattern
  * There are actual changes to files in the corresponding subdirectory (e.g., express-lane/**/*)

**Note:** Any other format is automatically ignored.

### Avoid execution due to irrelevant changes
Changes to files like `README.md` can be ignored if they don't affect directories monitored by the `rules`: in `.gitlab-ci.yml`. Make sure the modified file matches the paths configured in the changes: of each trigger.

### Questions or Suggestions?
Reach out to the DevOps team to add or modify deployment rules.
